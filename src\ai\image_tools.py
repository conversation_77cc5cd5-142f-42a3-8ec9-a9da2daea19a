"""
AI Image Tools for TikTok Automation
Image description and generation via PiAPI (Midjourney) API
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
import json
import base64

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. AI image tools will not be available.")

class PiAPIMidjourneyTools:
    """AI image tools using PiAPI (Midjourney) API"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize PiAPI Midjourney tools

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys'].get('piapi', '')
        self.describe_enabled = config['ai_workflow']['piapi_midjourney']['describe_enabled']
        self.generation_enabled = config['ai_workflow']['piapi_midjourney']['generation_enabled']
        self.image_variants = config['ai_workflow']['piapi_midjourney']['image_variants']
        self.select_best = config['ai_workflow']['piapi_midjourney']['select_best']

        # PiAPI endpoints
        self.base_url = "https://api.piapi.ai/api/v1"
        self.task_endpoint = f"{self.base_url}/task"
        self.describe_endpoint = f"{self.base_url}/mj/describe"
        self.imagine_endpoint = f"{self.base_url}/mj/imagine"

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=300.0,  # Longer timeout for Midjourney generation
            headers={
                "Authorization": f"Bearer {self.api_key}",
                "Content-Type": "application/json"
            }
        )

    async def describe_image(self, image_url: str) -> Dict[str, Any]:
        """Describe an image using PiAPI Midjourney describe

        Args:
            image_url: Public URL of the image to describe

        Returns:
            Dict containing image description and metadata
        """
        if not self.describe_enabled:
            raise ValueError("Image description is disabled in configuration")

        if not self.api_key:
            raise ValueError("PiAPI key not configured")

        logging.info(f"Describing image with PiAPI Midjourney: {image_url}")

        try:
            # Submit describe task
            payload = {
                "model": "midjourney",
                "task_type": "describe",
                "input": {
                    "image_url": image_url
                }
            }

            response = await self.client.post(self.task_endpoint, json=payload)
            response.raise_for_status()

            result = response.json()
            task_id = result.get('task_id')

            if not task_id:
                raise Exception("Failed to get task ID from PiAPI")

            # Poll for completion
            description_result = await self._wait_for_task_completion(task_id)

            # Format the response
            description_data = {
                'description': description_result.get('output', {}).get('description', ''),
                'task_id': task_id,
                'timestamp': time.time(),
                'provider': 'PiAPI Midjourney'
            }

            logging.info(f"Image description completed successfully")
            return description_data

        except Exception as e:
            logging.error(f"Error describing image with PiAPI: {str(e)}")
            raise

    async def generate_image(self, prompt: str, style_reference: Optional[str] = None) -> Dict[str, Any]:
        """Generate image using PiAPI Midjourney

        Args:
            prompt: Text prompt for image generation
            style_reference: Optional style reference

        Returns:
            Dict containing generated images and metadata
        """
        if not self.generation_enabled:
            raise ValueError("Image generation is disabled in configuration")

        if not self.api_key:
            raise ValueError("PiAPI key not configured")

        logging.info(f"Generating image via PiAPI Midjourney: {prompt[:100]}...")

        try:
            # Enhance prompt for anime-style TikTok content
            enhanced_prompt = f"{prompt} --ar 9:16 --style anime --v 6"

            # Submit imagine task
            payload = {
                "model": "midjourney",
                "task_type": "imagine",
                "input": {
                    "prompt": enhanced_prompt
                }
            }

            response = await self.client.post(self.task_endpoint, json=payload)
            response.raise_for_status()

            result = response.json()
            task_id = result.get('task_id')

            if not task_id:
                raise Exception("Failed to get task ID from PiAPI")

            # Poll for completion
            generation_result = await self._wait_for_task_completion(task_id)

            # Process PiAPI response
            images = []
            output = generation_result.get('output', {})

            if 'image_url' in output:
                images.append({
                    'url': output['image_url'],
                    'task_id': task_id,
                    'index': 0,
                    'quality_score': 95  # High score for Midjourney
                })

            generation_data = {
                'prompt': prompt,
                'enhanced_prompt': enhanced_prompt,
                'images': images,
                'best_image': images[0] if images else None,
                'generation_id': f"piapi_{task_id}",
                'provider': 'PiAPI Midjourney',
                'timestamp': time.time()
            }

            logging.info(f"Generated image successfully with PiAPI Midjourney")
            return generation_data

        except Exception as e:
            logging.error(f"Error generating image with PiAPI: {str(e)}")
            raise

    async def _wait_for_task_completion(self, task_id: str, max_wait_time: int = 300) -> Dict[str, Any]:
        """Wait for PiAPI task to complete

        Args:
            task_id: Task ID to monitor
            max_wait_time: Maximum time to wait in seconds

        Returns:
            Dict: Task result
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                # Check task status
                response = await self.client.get(f"{self.task_endpoint}/{task_id}")
                response.raise_for_status()

                result = response.json()
                status = result.get('status')

                if status == 'completed':
                    return result
                elif status == 'failed':
                    error_msg = result.get('error', 'Task failed')
                    raise Exception(f"PiAPI task failed: {error_msg}")

                # Wait before next check
                await asyncio.sleep(5)

            except Exception as e:
                if "Task failed" in str(e):
                    raise
                logging.warning(f"Error checking task status: {str(e)}")
                await asyncio.sleep(5)

        raise Exception(f"Task {task_id} timed out after {max_wait_time} seconds")

    async def _select_best_image(self, images: List[Dict], prompt: str) -> Dict[str, Any]:
        """Select the best image from generated variants

        Args:
            images: List of generated images
            prompt: Original prompt for context

        Returns:
            Dict: Best image data
        """
        # For now, use simple scoring based on image metadata
        # In a real implementation, this could use additional AI analysis

        best_image = images[0]
        best_score = 0

        for image in images:
            score = 0

            # Score based on quality metrics if available
            if 'quality_score' in image:
                score += image['quality_score'] * 0.4

            # Score based on resolution
            if 'width' in image and 'height' in image:
                aspect_ratio = image['height'] / image['width']
                # Prefer 9:16 aspect ratio for TikTok
                if 1.7 <= aspect_ratio <= 1.8:
                    score += 30
                elif 1.5 <= aspect_ratio <= 2.0:
                    score += 20

            # Score based on file size (reasonable size)
            if 'file_size' in image:
                size_mb = image['file_size'] / (1024 * 1024)
                if 1 <= size_mb <= 5:  # Good size range
                    score += 20

            if score > best_score:
                best_score = score
                best_image = image

        logging.info(f"Selected best image with score: {best_score}")
        return best_image

    async def download_image(self, image_data: Dict[str, Any], output_path: str) -> str:
        """Download/save generated image to local file

        Args:
            image_data: Image data dict (can contain URL or base64)
            output_path: Local path to save the image

        Returns:
            str: Path to downloaded image
        """
        logging.info(f"Saving image to: {output_path}")

        try:
            # Ensure output directory exists
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            # Handle different image data formats
            if 'url' in image_data and image_data['url'].startswith('http'):
                # PiAPI returns HTTP URLs
                response = await self.client.get(image_data['url'])
                response.raise_for_status()
                with open(output_path, 'wb') as f:
                    f.write(response.content)
            elif 'base64' in image_data:
                # Base64 data
                image_bytes = base64.b64decode(image_data['base64'])
                with open(output_path, 'wb') as f:
                    f.write(image_bytes)
            elif 'url' in image_data and image_data['url'].startswith('data:image'):
                # Data URL (base64 embedded)
                header, data = image_data['url'].split(',', 1)
                image_bytes = base64.b64decode(data)
                with open(output_path, 'wb') as f:
                    f.write(image_bytes)
            else:
                raise ValueError("Invalid image data format")

            logging.info(f"Image saved successfully: {output_path}")
            return output_path

        except Exception as e:
            logging.error(f"Error saving image: {str(e)}")
            raise

    async def enhance_prompt_with_description(self, base_prompt: str, description: str,
                                            niche: str) -> str:
        """Enhance image generation prompt using description

        Args:
            base_prompt: Base prompt for image generation
            description: Image description from describe API
            niche: Content niche for context

        Returns:
            str: Enhanced prompt
        """
        # Extract key visual elements from description
        visual_elements = self._extract_visual_elements(description)

        # Combine with base prompt and niche context
        enhanced_prompt = f"{base_prompt}, {visual_elements}, {niche} aesthetic, photorealistic, high quality, 9:16 aspect ratio, TikTok style"

        # Clean up and optimize prompt
        enhanced_prompt = self._optimize_prompt(enhanced_prompt)

        logging.info(f"Enhanced prompt: {enhanced_prompt}")
        return enhanced_prompt

    def _extract_visual_elements(self, description: str) -> str:
        """Extract key visual elements from image description

        Args:
            description: Image description text

        Returns:
            str: Extracted visual elements
        """
        # Simple keyword extraction - in production, this could be more sophisticated
        visual_keywords = []

        # Common visual descriptors
        descriptors = ['dark', 'bright', 'moody', 'atmospheric', 'dramatic', 'cinematic',
                      'detailed', 'realistic', 'stylized', 'vintage', 'modern', 'elegant']

        # Colors
        colors = ['red', 'blue', 'green', 'yellow', 'purple', 'orange', 'black', 'white',
                 'golden', 'silver', 'bronze', 'crimson', 'azure', 'emerald']

        # Lighting
        lighting = ['soft lighting', 'dramatic lighting', 'natural light', 'studio lighting',
                   'backlit', 'rim lighting', 'ambient light', 'harsh shadows']

        description_lower = description.lower()

        # Extract relevant keywords
        for keyword in descriptors + colors + lighting:
            if keyword in description_lower:
                visual_keywords.append(keyword)

        return ', '.join(visual_keywords[:5])  # Limit to top 5 elements

    def _optimize_prompt(self, prompt: str) -> str:
        """Optimize prompt for better generation results

        Args:
            prompt: Raw prompt text

        Returns:
            str: Optimized prompt
        """
        # Remove duplicates and clean up
        words = prompt.split(', ')
        unique_words = []
        seen = set()

        for word in words:
            word = word.strip()
            if word and word.lower() not in seen:
                unique_words.append(word)
                seen.add(word.lower())

        # Ensure key quality terms are included
        quality_terms = ['high quality', 'detailed', 'professional']
        for term in quality_terms:
            if not any(term in word.lower() for word in unique_words):
                unique_words.append(term)

        return ', '.join(unique_words)

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
