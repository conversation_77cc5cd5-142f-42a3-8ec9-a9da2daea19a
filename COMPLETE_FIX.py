#!/usr/bin/env python3
"""
COMPLETE FIX for TikTok Automation
Installs all dependencies and configures for real API usage
"""

import subprocess
import sys
import os
import json

def install_all_dependencies():
    """Install ALL required dependencies"""
    
    print("🔧 COMPLETE FIX: Installing ALL Dependencies...")
    print("=" * 60)
    
    # List of ALL required packages
    packages = [
        "gtts>=2.3.0",           # Google Text-to-Speech
        "moviepy>=1.0.3",        # Video editing
        "pyttsx3>=2.90",         # Better TTS engine
        "pydub>=0.25.1",         # Audio conversion
        "httpx>=0.24.0",         # HTTP client for APIs
        "aiofiles>=23.0.0",      # Async file operations
        "requests>=2.28.0",      # HTTP requests
        "pillow>=9.0.0",         # Image processing
        "opencv-python>=4.7.0",  # Video processing
        "numpy>=1.21.0",         # Numerical operations
        "asyncio-mqtt>=0.11.0"   # Async MQTT
    ]
    
    failed_packages = []
    
    for package in packages:
        print(f"📦 Installing {package}...")
        try:
            subprocess.check_call([sys.executable, "-m", "pip", "install", package], 
                                stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL)
            print(f"✅ Successfully installed {package}")
        except subprocess.CalledProcessError as e:
            print(f"❌ Failed to install {package}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️ Failed to install: {', '.join(failed_packages)}")
        print("Please install them manually:")
        for pkg in failed_packages:
            print(f"   pip install {pkg}")
        return False
    
    print("\n🎉 ALL dependencies installed successfully!")
    return True

def verify_config():
    """Verify configuration is set for real APIs"""
    print("\n🔍 Verifying configuration...")
    
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
        
        # Check providers
        tts_provider = config.get('tts', {}).get('provider')
        image_provider = config.get('image', {}).get('provider')
        
        print(f"   TTS Provider: {tts_provider}")
        print(f"   Image Provider: {image_provider}")
        
        # Check API keys
        api_keys = config.get('api_keys', {})
        required_keys = ['openai', 'apyhub', 'piapi', 'hedra']
        
        for key in required_keys:
            if api_keys.get(key):
                print(f"   ✅ {key.upper()} API key configured")
            else:
                print(f"   ❌ {key.upper()} API key missing")
        
        # Check if enhanced workflow is enabled
        ai_workflow = config.get('ai_workflow', {})
        apyhub_enabled = ai_workflow.get('apyhub_tts', {}).get('enabled', False)
        piapi_enabled = ai_workflow.get('piapi_midjourney', {}).get('generation_enabled', False)
        
        print(f"   ApyHub TTS: {'✅ Enabled' if apyhub_enabled else '❌ Disabled'}")
        print(f"   PiAPI Midjourney: {'✅ Enabled' if piapi_enabled else '❌ Disabled'}")
        
        if (tts_provider == 'apyhub' and image_provider == 'piapi_midjourney' and 
            apyhub_enabled and piapi_enabled):
            print("\n✅ Configuration is set for REAL API USAGE!")
            return True
        else:
            print("\n⚠️ Configuration needs adjustment for real APIs")
            return False
            
    except Exception as e:
        print(f"❌ Error checking config: {e}")
        return False

def test_imports():
    """Test if all imports work"""
    print("\n🧪 Testing imports...")
    
    imports_to_test = [
        ("gtts", "gTTS"),
        ("moviepy.editor", "MoviePy"),
        ("pyttsx3", "pyttsx3"),
        ("httpx", "httpx"),
        ("aiofiles", "aiofiles"),
        ("PIL", "Pillow"),
        ("cv2", "OpenCV"),
        ("requests", "requests"),
        ("numpy", "numpy")
    ]
    
    all_good = True
    for module, name in imports_to_test:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} - install failed")
            all_good = False
    
    return all_good

def create_test_script():
    """Create a test script for API verification"""
    test_script = '''#!/usr/bin/env python3
"""
Test Real API Connections
"""

import asyncio
import logging
import sys
import os

# Add project root to path
sys.path.insert(0, os.getcwd())

async def test_apis():
    """Test all API connections"""
    print("🧪 Testing Real API Connections...")
    print("=" * 50)
    
    # Load config
    import json
    with open('config.json', 'r') as f:
        config = json.load(f)
    
    # Test OpenAI
    print("\\n1. Testing OpenAI...")
    try:
        from src.ai.story_generator import EnhancedStoryGenerator
        story_gen = EnhancedStoryGenerator(config)
        print("   ✅ OpenAI connection ready")
    except Exception as e:
        print(f"   ❌ OpenAI error: {e}")
    
    # Test ApyHub
    print("\\n2. Testing ApyHub TTS...")
    try:
        from src.ai.audio_tools import ApyHubAudio
        audio_tools = ApyHubAudio(config)
        print("   ✅ ApyHub connection ready")
    except Exception as e:
        print(f"   ❌ ApyHub error: {e}")
    
    # Test PiAPI
    print("\\n3. Testing PiAPI Midjourney...")
    try:
        from src.ai.image_tools import PiAPIMidjourneyTools
        image_tools = PiAPIMidjourneyTools(config)
        print("   ✅ PiAPI connection ready")
    except Exception as e:
        print(f"   ❌ PiAPI error: {e}")
    
    # Test Hedra
    print("\\n4. Testing Hedra Video...")
    try:
        from src.ai.enhanced_video_assembler import EnhancedVideoAssembler
        video_assembler = EnhancedVideoAssembler(config)
        print("   ✅ Hedra connection ready")
    except Exception as e:
        print(f"   ❌ Hedra error: {e}")
    
    print("\\n🎉 API connection tests completed!")

if __name__ == "__main__":
    asyncio.run(test_apis())
'''
    
    with open('test_apis.py', 'w') as f:
        f.write(test_script)
    
    print("📝 Created test_apis.py script")

def main():
    """Main function"""
    print("🎬 TikTok Automation - COMPLETE FIX")
    print("=" * 60)
    print("This will:")
    print("• Install ALL required dependencies")
    print("• Verify configuration for real APIs")
    print("• Test all imports")
    print("• Create API test script")
    print("=" * 60)
    
    # Install dependencies
    deps_ok = install_all_dependencies()
    
    # Test imports
    imports_ok = test_imports()
    
    # Verify config
    config_ok = verify_config()
    
    # Create test script
    create_test_script()
    
    print("\n" + "=" * 60)
    print("🎯 COMPLETE FIX SUMMARY:")
    print(f"   Dependencies: {'✅ OK' if deps_ok else '❌ FAILED'}")
    print(f"   Imports: {'✅ OK' if imports_ok else '❌ FAILED'}")
    print(f"   Configuration: {'✅ OK' if config_ok else '⚠️ NEEDS ADJUSTMENT'}")
    
    if deps_ok and imports_ok and config_ok:
        print("\n🎉 EVERYTHING IS READY!")
        print("\n🚀 Next steps:")
        print("1. Run: python test_apis.py  (to test API connections)")
        print("2. Run: python main.py       (to start the app)")
        print("\n📋 Test with this data:")
        print("   Account: test_real_apis")
        print("   Niche: horror")
        print("   Theme: Scary Stories")
        print("   Topic: A mirror that shows your future instead of your reflection")
        print("   Voice: female")
        print("\n🎬 Expected output:")
        print("   • Real OpenAI story generation")
        print("   • Real ApyHub high-quality audio")
        print("   • Real PiAPI Midjourney images")
        print("   • Real Hedra video assembly")
        print("   • Professional TikTok video!")
    else:
        print("\n⚠️ SOME ISSUES NEED TO BE FIXED")
        if not deps_ok:
            print("   - Install missing dependencies manually")
        if not imports_ok:
            print("   - Fix import errors")
        if not config_ok:
            print("   - Check API keys in config.json")
    
    input("\\nPress Enter to continue...")

if __name__ == "__main__":
    main()
