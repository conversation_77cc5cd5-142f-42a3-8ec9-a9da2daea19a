"""
ApyHub Audio Tools for TikTok Automation
Text-to-Speech and audio generation via ApyHub API
"""

import os
import logging
import time
import asyncio
from typing import Dict, Any, Optional, List
import json

try:
    import httpx
except ImportError:
    logging.warning("httpx package not installed. ApyHub audio tools will not be available.")

class ApyHubAudio:
    """ApyHub audio generation service"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize ApyHub audio service

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.api_key = config['api_keys']['apyhub']

        # API endpoints
        self.base_url = "https://api.apyhub.com"
        self.tts_endpoint = f"{self.base_url}/convert/text-to-audio"

        # ApyHub voice configurations (Azure Neural Voices)
        self.voice_mapping = {
            'male': 'en-US-AriaNeural',
            'female': 'en-US-JennyNeural',
            'ai': 'en-US-AriaNeural',
            'male1': 'en-US-AriaNeural',
            'male2': 'en-US-DavisNeural',
            'female1': 'en-US-JennyNeural',
            'female2': 'en-US-AmberNeural',
            'ai1': 'en-US-AriaNeural',
            'ai2': 'en-US-JennyNeural'
        }

        # Voice names for display
        self.voice_names = {
            'male': 'Aria (Versatile Voice)',
            'female': 'Jenny (Clear Female)',
            'ai': 'Aria (Professional)',
            'male1': 'Aria (Versatile)',
            'male2': 'Davis (Deep Male)',
            'female1': 'Jenny (Clear Female)',
            'female2': 'Amber (Warm Female)',
            'ai1': 'Aria (Professional)',
            'ai2': 'Jenny (Natural)'
        }

        # Initialize HTTP client
        self.client = httpx.AsyncClient(
            timeout=60.0,
            headers={
                "apy-token": self.api_key,
                "Content-Type": "application/json"
            }
        )

        # Output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))), "data", "audio")
        os.makedirs(self.output_dir, exist_ok=True)

    async def generate_speech(self, text: str, voice_type: str,
                            output_filename: Optional[str] = None) -> str:
        """Generate speech from text using ApyHub TTS

        Args:
            text: Text to convert to speech
            voice_type: Voice type (male, female, ai, etc.)
            output_filename: Optional filename for output

        Returns:
            str: Path to generated audio file
        """
        if not self.api_key:
            raise ValueError("ApyHub API key not configured")

        logging.info(f"Generating speech with ApyHub: {len(text)} chars, voice: {voice_type}")
        logging.debug(f"Using API key: {self.api_key[:10]}...{self.api_key[-4:]}")  # Log partial key for debugging

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"speech_{timestamp}.mp3"

        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Map voice type to ApyHub voice
            voice = self.voice_mapping.get(voice_type, self.voice_mapping['ai'])

            # Prepare request payload for ApyHub
            payload = {
                "text": text,
                "voice": voice,
                "format": "mp3",
                "speed": 1.0,
                "pitch": 1.0
            }

            # Make API request
            response = await self.client.post(
                self.tts_endpoint,
                json=payload,
                headers={
                    "apy-token": self.api_key,
                    "Content-Type": "application/json"
                }
            )

            # Better error handling
            if response.status_code != 200:
                error_text = response.text
                logging.error(f"ApyHub API error {response.status_code}: {error_text}")
                try:
                    error_json = response.json()
                    error_msg = error_json.get('message', error_text)
                except:
                    error_msg = error_text
                raise Exception(f"ApyHub API error {response.status_code}: {error_msg}")

            response.raise_for_status()

            # ApyHub returns JSON with download URL
            result = response.json()
            if 'data' in result and 'url' in result['data']:
                download_url = result['data']['url']

                # Download the audio file
                download_response = await self.client.get(download_url)
                download_response.raise_for_status()

                # Save audio file
                with open(output_path, 'wb') as f:
                    f.write(download_response.content)

                logging.info(f"Speech generated successfully: {output_path}")
                return output_path
            else:
                raise Exception(f"ApyHub TTS failed: Invalid response format")

        except Exception as e:
            logging.error(f"Error generating speech with ApyHub: {str(e)}")
            raise

    async def get_available_voices(self) -> List[Dict[str, Any]]:
        """Get available voices from ApyHub

        Returns:
            List: Available voices with metadata
        """
        # Return predefined Azure Neural Voices available through ApyHub
        voices = []
        for voice_type, voice_id in self.voice_mapping.items():
            voices.append({
                'voice_id': voice_id,
                'name': self.voice_names.get(voice_type, voice_id),
                'category': voice_type,
                'description': f'Azure Neural Voice: {voice_id}',
                'preview_url': '',
                'labels': {'type': voice_type, 'provider': 'azure'}
            })

        logging.info(f"Retrieved {len(voices)} voices from ApyHub")
        return voices

    async def clone_voice(self, name: str, audio_files: List[str],
                         description: str = "") -> Dict[str, Any]:
        """Voice cloning not supported by ApyHub

        Args:
            name: Name for the cloned voice
            audio_files: List of audio file paths for training
            description: Description of the voice

        Returns:
            Dict: Error information
        """
        raise NotImplementedError("Voice cloning is not supported by ApyHub. Use predefined Azure Neural Voices instead.")

    async def get_character_count(self, text: str) -> Dict[str, Any]:
        """Get character count and cost estimation for text

        Args:
            text: Text to analyze

        Returns:
            Dict: Character count and cost information
        """
        char_count = len(text)

        # ApyHub pricing (approximate)
        # Pay-per-use model
        cost_per_char = 0.0001  # Approximate cost for ApyHub
        estimated_cost = char_count * cost_per_char

        return {
            'character_count': char_count,
            'estimated_cost_usd': round(estimated_cost, 4),
            'provider': 'ApyHub'
        }

    async def enhance_audio(self, audio_path: str,
                          enhancement_type: str = "normalize") -> str:
        """Enhance audio quality using basic processing

        Args:
            audio_path: Path to audio file
            enhancement_type: Type of enhancement (normalize, denoise, etc.)

        Returns:
            str: Path to enhanced audio file
        """
        if not os.path.exists(audio_path):
            raise FileNotFoundError(f"Audio file not found: {audio_path}")

        logging.info(f"Enhancing audio: {audio_path} (type: {enhancement_type})")

        # Generate output filename
        base_name = os.path.splitext(os.path.basename(audio_path))[0]
        output_filename = f"{base_name}_enhanced.mp3"
        output_path = os.path.join(self.output_dir, output_filename)

        try:
            # Basic audio enhancement (copy for now)
            # In production, could use pydub or other audio processing libraries
            import shutil
            shutil.copy2(audio_path, output_path)

            logging.info(f"Audio enhanced: {output_path}")
            return output_path

        except Exception as e:
            logging.error(f"Error enhancing audio: {str(e)}")
            raise

    def get_voice_options(self) -> Dict[str, List[str]]:
        """Get available voice options

        Returns:
            Dict: Voice options organized by category
        """
        return {
            'male': ['male', 'male1', 'male2'],
            'female': ['female', 'female1', 'female2'],
            'ai': ['ai', 'ai1', 'ai2']
        }

    def get_voice_names(self) -> Dict[str, str]:
        """Get voice names for display

        Returns:
            Dict: Voice type to display name mapping
        """
        return self.voice_names

    def estimate_speech_duration(self, text: str, voice_type: str = 'ai') -> float:
        """Estimate speech duration for given text

        Args:
            text: Text to analyze
            voice_type: Voice type for speed estimation

        Returns:
            float: Estimated duration in seconds
        """
        # Azure Neural Voice speaking rates (words per minute)
        speaking_rates = {
            'male': 160,      # Aria - versatile pace
            'female': 170,    # Jenny - clear and natural
            'ai': 165,        # Aria - professional pace
            'male1': 160,     # Aria
            'male2': 155,     # Davis - deeper, slower
            'female1': 170,   # Jenny
            'female2': 165,   # Amber
            'ai1': 165,       # Aria
            'ai2': 170        # Jenny
        }

        rate = speaking_rates.get(voice_type, 160)
        word_count = len(text.split())
        duration = (word_count / rate) * 60

        return duration

    def get_model_options(self) -> List[str]:
        """Get available ApyHub voice models

        Returns:
            List: Available voice IDs
        """
        return list(set(self.voice_mapping.values()))

    async def close(self):
        """Close the HTTP client"""
        await self.client.aclose()

    def __del__(self):
        """Cleanup on destruction"""
        try:
            asyncio.create_task(self.close())
        except:
            pass
