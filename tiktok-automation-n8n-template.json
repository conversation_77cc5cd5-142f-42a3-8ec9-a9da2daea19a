{"name": "TikTok Automation Pipeline - Simplified", "nodes": [{"parameters": {"content": "# TikTok Automation Pipeline\n\n## Workflow Overview:\n1. **OpenAI (GPT-4)** → Generate viral TikTok stories\n2. **ApyHub** → Convert script to audio (Azure Neural Voices)\n3. **OpenAI (GPT-4)** → Create visual scene prompts\n4. **PiAPI (Midjourney)** → Generate images with anime style\n5. **Hedra** → Combine audio + image into video\n6. **Google Drive** → Upload final video\n\n## Required APIs:\n- OpenAI API Key\n- ApyHub API Key\n- PiAPI API Key\n- Hedra API Key\n- ImgBB API Key (for image hosting)\n\n## Output:\n- TikTok-ready videos (9:16 aspect ratio)\n- Anime-style moving images\n- High-quality audio narration\n- Automated Google Drive upload", "height": 680, "width": 800, "color": 1}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-200, 200], "id": "workflow-overview", "name": "Workflow Overview"}, {"parameters": {"content": "## OpenAI API Setup\n\n**Purpose**: Story generation & prompt engineering using GPT-4\n\n**Where to get API Key**:\n1. Visit: https://platform.openai.com\n2. Go to API Keys section\n3. Create new secret key\n4. Add billing method for GPT-4 access\n\n**Cost**: ~$0.03 per 1K tokens (GPT-4)\n\n**Features**:\n• Viral TikTok story generation\n• Scene-to-visual prompt transformation\n• Baby transformation prompts\n• Advanced prompt engineering", "height": 400, "width": 600, "color": 2}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1200, 600], "id": "openai-info", "name": "OpenAI Info"}, {"parameters": {"content": "## ApyHub API Setup\n\n**Purpose**: Text-to-audio conversion using Azure Neural Voices\n\n**Where to get API Key**:\n1. Visit: https://apyhub.com\n2. Go to Workspace → API Keys\n3. Generate new API key\n\n**Cost**: ~$0.0001 per character\n\n**Features**:\n• Azure Neural Voices (<PERSON>, <PERSON>, <PERSON>, <PERSON>)\n• High-quality TTS\n• MP3 output format\n• Multiple voice options", "height": 400, "width": 600, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-600, 600], "id": "apyhub-info", "name": "ApyHub Info"}, {"parameters": {"content": "## PiAPI (Midjourney) Setup\n\n**Purpose**: Real Midjourney image generation & description\n\n**Where to get API Key**:\n1. Visit: https://piapi.ai\n2. Go to Workspace → API Keys\n3. Generate new API key\n4. Add billing/credits\n\n**Cost**: ~$0.05-0.10 per image\n\n**Features**:\n• Real Midjourney describe function\n• High-quality image generation\n• Anime style optimization\n• 9:16 aspect ratio support", "height": 400, "width": 600, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [0, 600], "id": "piapi-info", "name": "PiAPI Info"}, {"parameters": {"content": "## Hedra API Setup\n\n**Purpose**: Video assembly from audio + image\n\n**Where to get API Key**:\n1. Visit: https://www.hedra.com/api-profile\n2. Sign up and purchase plan\n3. Get API key from dashboard\n\n**Cost**: ~$0.10-0.50 per video\n\n**Features**:\n• Audio + image combination\n• Anime-style animation\n• 9:16 TikTok format\n• Professional quality output", "height": 400, "width": 600, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [600, 600], "id": "hedra-info", "name": "<PERSON><PERSON>"}, {"parameters": {"pollTimes": {"item": [{"mode": "everyMinute"}]}, "triggerOn": "webhook", "httpMethod": "POST", "path": "tiktok-automation", "options": {}}, "type": "n8n-nodes-base.webhook", "typeVersion": 2, "position": [-1400, 1200], "id": "webhook-trigger", "name": "Webhook Trigger", "webhookId": "tiktok-automation-trigger"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "You are an expert TikTok content creator specializing in viral, engaging stories. Create compelling narratives that capture attention within the first 3 seconds and maintain engagement throughout. Focus on emotional hooks, relatable scenarios, and trending topics.", "role": "system"}, {"content": "Generate a viral TikTok story script about {{ $json.topic || 'a mysterious adventure' }}. The script should be:\n\n1. 45-60 seconds when spoken\n2. Include emotional hooks in the first 5 seconds\n3. Use simple, conversational language\n4. Include dramatic pauses and emphasis\n5. End with a compelling conclusion or cliffhanger\n6. Optimized for anime-style visual content\n\nFormat: Return only the script text, no additional formatting or explanations.", "role": "user"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1200, 1200], "id": "generate-story", "name": "Generate TikTok Story", "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"method": "POST", "url": "https://api.apyhub.com/convert/text-to-audio", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apy-token", "value": "={{ $credentials.apyhub.token }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"text\": \"{{ $json.message.content }}\",\n  \"voice\": \"en-US-AriaNeural\",\n  \"format\": \"mp3\",\n  \"speed\": 1.0,\n  \"pitch\": 1.0\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1000, 1200], "id": "convert-to-audio", "name": "ApyHub - Text to Audio"}, {"parameters": {"amount": 30}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-800, 1200], "id": "wait-audio", "name": "Wait for Audio", "webhookId": "wait-audio-processing"}, {"parameters": {"url": "={{ $json.data.url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-600, 1200], "id": "download-audio", "name": "Download Audio File"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "You are an expert prompt engineer specializing in creating anime-style visual prompts for TikTok content. Transform story scripts into vivid, cinematic visual descriptions optimized for Midjourney image generation.", "role": "system"}, {"content": "Transform this TikTok story script into a compelling visual prompt for anime-style image generation:\n\nScript: {{ $('Generate TikTok Story').item.json.message.content }}\n\nCreate a visual prompt that:\n1. Captures the main character/scene from the story\n2. Uses anime art style with dramatic lighting\n3. Optimized for 9:16 aspect ratio (vertical)\n4. Includes cinematic composition\n5. Emphasizes emotional expression and atmosphere\n6. Suitable for animation/movement\n\nReturn only the visual prompt, no explanations. Keep under 500 characters.", "role": "user"}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-400, 1200], "id": "create-visual-prompt", "name": "Create Visual Prompt", "credentials": {"openAiApi": {"id": "openai-credentials", "name": "OpenAI API"}}}, {"parameters": {"method": "POST", "url": "https://api.piapi.ai/api/v1/task", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key", "value": "={{ $credentials.piapi.apiKey }}"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"{{ $json.message.content }} --ar 9:16 --style anime --v 6\",\n    \"aspect_ratio\": \"9:16\",\n    \"process_mode\": \"fast\",\n    \"skip_prompt_check\": true\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-200, 1200], "id": "generate-image", "name": "PiAPI - Generate Image"}, {"parameters": {"amount": 5, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [0, 1200], "id": "wait-image", "name": "Wait for Image", "webhookId": "wait-image-generation"}, {"parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key", "value": "={{ $credentials.piapi.apiKey }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [200, 1200], "id": "get-image-result", "name": "Get Image Result"}, {"parameters": {"url": "={{ $json.data.output.image_url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [400, 1200], "id": "download-image", "name": "Download Generated Image"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/assets", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "={{ $credentials.hedra.api<PERSON>ey }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"name\": \"tiktok-image\",\n  \"type\": \"image\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 1100], "id": "create-image-asset", "name": "Hedra - Create Image Asset"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/assets", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "={{ $credentials.hedra.api<PERSON>ey }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"name\": \"tiktok-audio\",\n  \"type\": \"audio\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [600, 1300], "id": "create-audio-asset", "name": "Hedra - Create Audio Asset"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [800, 1200], "id": "merge-assets", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "const imageAsset = items.find(item => item.json.type === 'image');\nconst audioAsset = items.find(item => item.json.type === 'audio');\nconst imageBinary = items.find(item => item.binary && item.binary.data);\nconst audioBinary = items.find(item => item.binary && item.binary.data && item.json.type !== 'image');\n\nreturn [\n  {\n    json: {\n      imageAssetId: imageAsset?.json.id,\n      audioAssetId: audioAsset?.json.id\n    },\n    binary: {\n      image: imageBinary?.binary.data,\n      audio: audioBinary?.binary.data\n    }\n  }\n];", "options": {}}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1000, 1200], "id": "prepare-assets", "name": "Prepare <PERSON>"}, {"parameters": {"method": "POST", "url": "=https://api.hedra.com/web-app/public/assets/{{ $json.imageAssetId }}/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "={{ $credentials.hedra.api<PERSON>ey }}"}, {"name": "Content-Type", "value": "multipart/form-data"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "image"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 1100], "id": "upload-image-asset", "name": "Upload Image to <PERSON><PERSON>"}, {"parameters": {"method": "POST", "url": "=https://api.hedra.com/web-app/public/assets/{{ $json.audioAssetId }}/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "={{ $credentials.hedra.api<PERSON>ey }}"}, {"name": "Content-Type", "value": "multipart/form-data"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "audio"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1200, 1300], "id": "upload-audio-asset", "name": "Upload Audio to <PERSON>dra"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [1400, 1200], "id": "merge-uploaded-assets", "name": "Merge Uploaded Assets"}, {"parameters": {"jsCode": "const imageUpload = items.find(item => item.json && item.json.id && item.json.type === 'image');\nconst audioUpload = items.find(item => item.json && item.json.id && item.json.type === 'audio');\n\nreturn [\n  {\n    json: {\n      imageAssetId: imageUpload?.json.id,\n      audioAssetId: audioUpload?.json.id\n    }\n  }\n];", "options": {}}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [1600, 1200], "id": "extract-asset-ids", "name": "Extract Asset IDs"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/generations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "={{ $credentials.hedra.api<PERSON>ey }}"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"type\": \"video\",\n  \"ai_model_id\": \"d1dd37a3-e39a-4854-a298-6510289f9cf2\",\n  \"start_keyframe_id\": \"{{ $json.imageAssetId }}\",\n  \"audio_id\": \"{{ $json.audioAssetId }}\",\n  \"generated_video_inputs\": {\n    \"text_prompt\": \"Anime-style character speaking with natural expressions and subtle movements, cinematic lighting, TikTok vertical format\",\n    \"resolution\": \"720p\",\n    \"aspect_ratio\": \"9:16\",\n    \"duration_ms\": 60000\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1800, 1200], "id": "generate-video", "name": "Hedra - Generate Video"}, {"parameters": {"amount": 8, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [2000, 1200], "id": "wait-video", "name": "Wait for Video", "webhookId": "wait-video-generation"}, {"parameters": {"url": "=https://api.hedra.com/web-app/public/assets?type=video&ids={{ $json.asset_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key", "value": "={{ $credentials.hedra.api<PERSON>ey }}"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2200, 1200], "id": "get-video-result", "name": "Get Video Result"}, {"parameters": {"url": "={{ $json.asset.url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [2400, 1200], "id": "download-video", "name": "Download Final Video"}, {"parameters": {"name": "=tiktok-video-{{ new Date().getTime() }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "REPLACE_WITH_YOUR_FOLDER_ID", "mode": "list", "cachedResultName": "TikTok Videos", "cachedResultUrl": "https://drive.google.com/drive/folders/REPLACE_WITH_YOUR_FOLDER_ID"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [2600, 1200], "id": "upload-to-drive", "name": "Upload to Google Drive", "credentials": {"googleDriveOAuth2Api": {"id": "google-drive-credentials", "name": "Google Drive"}}}, {"parameters": {"content": "# Video Generation Complete!\n\n✅ **TikTok Video Successfully Created**\n\n**Pipeline Steps Completed:**\n1. ✅ Story generated with OpenAI GPT-4\n2. ✅ Audio created with ApyHub (Azure Neural Voices)\n3. ✅ Visual prompt created with OpenAI GPT-4\n4. ✅ Image generated with PiAPI (Midjourney)\n5. ✅ Video assembled with <PERSON><PERSON>\n6. ✅ Uploaded to Google Drive\n\n**Output:**\n- Format: MP4 (9:16 aspect ratio)\n- Duration: ~60 seconds\n- Style: Anime with natural movement\n- Quality: 720p TikTok-ready\n\n**Next Steps:**\n- Download from Google Drive\n- Upload to TikTok\n- Add captions/hashtags\n- Schedule posting", "height": 600, "width": 500, "color": 6}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [2800, 1000], "id": "completion-note", "name": "Completion Note"}], "pinData": {}, "connections": {"Webhook Trigger": {"main": [[{"node": "Generate TikTok Story", "type": "main", "index": 0}]]}, "Generate TikTok Story": {"main": [[{"node": "ApyHub - Text to Audio", "type": "main", "index": 0}, {"node": "Create Visual Prompt", "type": "main", "index": 0}]]}, "ApyHub - Text to Audio": {"main": [[{"node": "Wait for Audio", "type": "main", "index": 0}]]}, "Wait for Audio": {"main": [[{"node": "Download Audio File", "type": "main", "index": 0}]]}, "Download Audio File": {"main": [[{"node": "Hedra - Create Audio Asset", "type": "main", "index": 0}]]}, "Create Visual Prompt": {"main": [[{"node": "PiAPI - Generate Image", "type": "main", "index": 0}]]}, "PiAPI - Generate Image": {"main": [[{"node": "Wait for Image", "type": "main", "index": 0}]]}, "Wait for Image": {"main": [[{"node": "Get Image Result", "type": "main", "index": 0}]]}, "Get Image Result": {"main": [[{"node": "Download Generated Image", "type": "main", "index": 0}]]}, "Download Generated Image": {"main": [[{"node": "Hedra - Create Image Asset", "type": "main", "index": 0}]]}, "Hedra - Create Image Asset": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Hedra - Create Audio Asset": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge Assets": {"main": [[{"node": "Prepare <PERSON>", "type": "main", "index": 0}]]}, "Prepare Assets": {"main": [[{"node": "Upload Image to <PERSON><PERSON>", "type": "main", "index": 0}, {"node": "Upload Audio to <PERSON>dra", "type": "main", "index": 0}]]}, "Upload Image to Hedra": {"main": [[{"node": "Merge Uploaded Assets", "type": "main", "index": 0}]]}, "Upload Audio to Hedra": {"main": [[{"node": "Merge Uploaded Assets", "type": "main", "index": 1}]]}, "Merge Uploaded Assets": {"main": [[{"node": "Extract Asset IDs", "type": "main", "index": 0}]]}, "Extract Asset IDs": {"main": [[{"node": "Hedra - Generate Video", "type": "main", "index": 0}]]}, "Hedra - Generate Video": {"main": [[{"node": "Wait for Video", "type": "main", "index": 0}]]}, "Wait for Video": {"main": [[{"node": "Get Video Result", "type": "main", "index": 0}]]}, "Get Video Result": {"main": [[{"node": "Download Final Video", "type": "main", "index": 0}]]}, "Download Final Video": {"main": [[{"node": "Upload to Google Drive", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "meta": {"templateCredsSetupCompleted": false}, "tags": [{"name": "tiktok-automation", "id": "tiktok-tag"}]}