"""
Text-to-Speech Generator for TikTok Automation
Converts text to speech using gTTS (Google Text-to-Speech)
"""

import os
import logging
import time
from typing import Dict, Any, Optional

try:
    from gtts import gTTS
    GTTS_AVAILABLE = True
except ImportError:
    GTTS_AVAILABLE = False
    logging.warning("gTTS not installed. Install with: pip install gtts"), List
import tempfile
import shutil

# For gTTS library
try:
    from gtts import gTTS
except ImportError:
    logging.warning("gTTS package not installed. Speech generation will not be available.")

# For audio processing - not strictly needed with gTTS
# We'll remove this dependency since we're using gTTS which doesn't require numpy/scipy
# This will eliminate the warning message

class SpeechGenerator:
    """Generates speech from text for TikTok videos"""

    def __init__(self, config: Dict[str, Any]):
        """Initialize speech generator

        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.provider = 'pyttsx3'  # We're using pyttsx3 for better quality
        self.voice_options = config['tts']['voice_options']

        # Create output directory
        self.output_dir = os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))),
                                      "data", "audio")
        os.makedirs(self.output_dir, exist_ok=True)

        # Initialize pyttsx3
        self._init_pyttsx3()

    def _init_pyttsx3(self):
        """Initialize pyttsx3 for better quality TTS"""
        try:
            import pyttsx3
            self.engine = pyttsx3.init()

            # Get available voices
            voices = self.engine.getProperty('voices')
            self.available_voices = {}

            # Categorize voices by gender/type
            for i, voice in enumerate(voices):
                voice_name = voice.name.lower()
                voice_id = voice.id

                if 'female' in voice_name or 'woman' in voice_name or 'zira' in voice_name:
                    self.available_voices['female'] = voice_id
                    self.available_voices['female1'] = voice_id
                    self.available_voices['female2'] = voice_id
                elif 'male' in voice_name or 'man' in voice_name or 'david' in voice_name:
                    self.available_voices['male'] = voice_id
                    self.available_voices['male1'] = voice_id
                    self.available_voices['male2'] = voice_id

                # Use first voice as AI voice
                if 'ai' not in self.available_voices:
                    self.available_voices['ai'] = voice_id
                    self.available_voices['ai1'] = voice_id
                    self.available_voices['ai2'] = voice_id

            # Set default properties for better quality
            self.engine.setProperty('rate', 180)  # Speed of speech
            self.engine.setProperty('volume', 0.9)  # Volume level

            logging.info(f"Initialized pyttsx3 with {len(voices)} voices")

        except ImportError:
            logging.warning("pyttsx3 not installed, falling back to gTTS")
            self.provider = 'gtts'
            self._init_gtts()
        except Exception as e:
            logging.error(f"Error initializing pyttsx3: {str(e)}")
            self.provider = 'gtts'
            self._init_gtts()

    def _init_gtts(self):
        """Initialize gTTS"""
        try:
            # No initialization needed for gTTS
            logging.info("Initialized gTTS speech generator")

            # Define available languages/accents
            self.available_langs = {
                'male': ['en-us', 'en-uk', 'en-au'],
                'female': ['en-us', 'en-uk', 'en-au'],
                'ai': ['en-us']
            }

        except Exception as e:
            logging.error(f"Error initializing gTTS: {str(e)}")
            raise

    def generate_speech(self, text: str, voice_type: str, output_filename: Optional[str] = None) -> str:
        """Generate speech from text

        Args:
            text: Text to convert to speech
            voice_type: Type of voice to use (e.g., "male", "female", "ai")
            output_filename: Optional filename for the output audio file

        Returns:
            str: Path to the generated audio file
        """
        logging.info(f"Generating speech for text ({len(text)} chars) with voice type: {voice_type}")

        # Select language/accent based on voice_type
        lang = self._select_language(voice_type)

        # Generate filename if not provided
        if not output_filename:
            timestamp = int(time.time())
            output_filename = f"speech_{timestamp}.mp3"  # gTTS generates mp3 files

        output_path = os.path.join(self.output_dir, output_filename)

        # Generate speech using selected provider
        if self.provider == 'pyttsx3':
            self._generate_with_pyttsx3(text, voice_type, output_path)
        else:
            # Fallback to gTTS
            lang = self._select_language(voice_type)
            self._generate_with_gtts(text, lang, output_path)

        logging.info(f"Generated speech saved to: {output_path}")
        return output_path

    def _select_language(self, voice_type: str) -> str:
        """Select a language/accent based on voice type

        Args:
            voice_type: Type of voice (e.g., "male", "female", "ai")

        Returns:
            str: Selected language code
        """
        # Get available languages for the requested type
        available_langs = self.available_langs.get(voice_type.lower(), [])

        if not available_langs:
            logging.warning(f"No languages available for type '{voice_type}', using default")
            return 'en-us'  # Default to US English

        # Return the first language of the requested type
        return available_langs[0]

    def _generate_with_pyttsx3(self, text: str, voice_type: str, output_path: str) -> None:
        """Generate speech using pyttsx3 for better quality

        Args:
            text: Text to convert to speech
            voice_type: Type of voice (male, female, ai, etc.)
            output_path: Path to save the generated audio
        """
        try:
            # Set voice based on type
            if voice_type in self.available_voices:
                voice_id = self.available_voices[voice_type]
                self.engine.setProperty('voice', voice_id)

            # Adjust speech rate based on voice type
            if voice_type in ['female', 'female1', 'female2']:
                self.engine.setProperty('rate', 170)  # Slightly faster for female
            elif voice_type in ['male', 'male1', 'male2']:
                self.engine.setProperty('rate', 160)  # Slightly slower for male
            else:
                self.engine.setProperty('rate', 175)  # Default for AI

            # Save to file
            self.engine.save_to_file(text, output_path)
            self.engine.runAndWait()

            # Convert to MP3 if needed (pyttsx3 usually saves as WAV)
            if output_path.endswith('.mp3') and not os.path.exists(output_path):
                # Try to find the WAV file and convert it
                wav_path = output_path.replace('.mp3', '.wav')
                if os.path.exists(wav_path):
                    try:
                        from pydub import AudioSegment
                        audio = AudioSegment.from_wav(wav_path)
                        audio.export(output_path, format="mp3")
                        os.remove(wav_path)  # Remove WAV file
                    except ImportError:
                        # If pydub not available, just rename WAV to MP3
                        os.rename(wav_path, output_path)

        except Exception as e:
            logging.error(f"Error generating speech with pyttsx3: {str(e)}")
            # Fallback to gTTS
            lang = self._select_language(voice_type)
            self._generate_with_gtts(text, lang, output_path)

    def _generate_with_gtts(self, text: str, lang: str, output_path: str) -> None:
        """Generate speech using gTTS

        Args:
            text: Text to convert to speech
            lang: Language code (e.g., 'en-us')
            output_path: Path to save the generated audio
        """
        try:
            # Process text to handle gTTS limitations
            processed_text = self._preprocess_text(text)

            # Parse language and accent
            if '-' in lang:
                tld = lang.split('-')[1]
                lang_code = lang.split('-')[0]
            else:
                tld = 'com'  # Default to .com TLD
                lang_code = lang

            # Generate speech
            if not GTTS_AVAILABLE:
                raise ImportError("gTTS not available")
            tts = gTTS(text=processed_text, lang=lang_code, tld=tld)

            # Save to file
            tts.save(output_path)

            # Convert to WAV if needed (some applications work better with WAV)
            # This is optional and can be removed if MP3 works fine
            if output_path.endswith('.mp3') and False:  # Disabled for now
                try:
                    wav_path = output_path.replace('.mp3', '.wav')
                    from pydub import AudioSegment
                    sound = AudioSegment.from_mp3(output_path)
                    sound.export(wav_path, format="wav")
                    return wav_path
                except ImportError:
                    logging.warning("pydub not installed, keeping MP3 format")

        except Exception as e:
            logging.error(f"Error generating speech with gTTS: {str(e)}")
            raise

    def _preprocess_text(self, text: str) -> str:
        """Preprocess text for TTS

        Args:
            text: Original text

        Returns:
            str: Processed text suitable for TTS
        """
        # Remove special characters that might cause issues
        processed_text = text.replace('"', '').replace('"', '')

        # Split into sentences if text is too long
        if len(processed_text) > 5000:  # gTTS has a higher limit than other TTS systems
            logging.info("Text is long, splitting into chunks for processing")
            return self._split_into_sentences(processed_text)

        return processed_text

    def _split_into_sentences(self, text: str) -> str:
        """Split long text into sentences

        Args:
            text: Long text

        Returns:
            str: Text with proper sentence breaks
        """
        # Simple sentence splitting
        sentences = []
        for sentence in text.replace('. ', '.\n').replace('! ', '!\n').replace('? ', '?\n').split('\n'):
            if sentence.strip():
                sentences.append(sentence.strip())

        return '. '.join(sentences)

    def list_available_voices(self) -> Dict[str, List[str]]:
        """List all available voices

        Returns:
            Dict: Dictionary of voice types and available languages
        """
        return self.available_langs
