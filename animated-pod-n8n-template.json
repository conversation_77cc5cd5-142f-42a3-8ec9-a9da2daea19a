{"name": "Animated Pod TikTok Hedra Template", "nodes": [{"parameters": {"content": "# Important\n\n## About APIs\n\nMidjourney uses image_url\nHe<PERSON> uses binary data\n\nNeed to store midjourney as binary and upload to he<PERSON> before compiling image and audio\n\n## Can't pin binary data\n\nVideo to audio pinned converts into json\nCauses error downstream, in code node if try to grab binary data, this will be undefined.\n\n## Convert Mime Type:\n\nbinary/octet-stream -> mp4", "height": 680, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [1760, 2620], "id": "f0f18b19-d368-463f-beac-2f0c97cfdb9b", "name": "Sticky Note13"}, {"parameters": {"content": "## Convert Video to Audio Using ApyHub\n\n## ApyHub API KEY\n\nStep-by-Step: Create an API Key on ApyHub\n\n1. Log in to ApyHub  \n   - Visit: https://apyhub.com  \n   - Sign in with your ApyHub account (or create one if you don't have one)\n\n2. Navigate to the API Keys Page\n   - Expand \"My Workspace\"  \n   - Click the “API Keys” tab on the left side of the dashboard\n\n3. Create a New API Key  \n   - Click the “Generate API Key” button  \n   - Optionally name it for easy identification  \n   - Click “Generate”  \n   - Copy and save your API key securely\n\n## Convert Video to Audio Using ApyHub\n\n1. Use the Video-to-Audio Endpoint  \n   - Endpoint: https://api.apyhub.com/extract/video/audio/file\n   - Documentation: https://apyhub.com/utility/extract-video-audio", "height": 1860, "width": 740, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-680, 1440], "id": "9181ae50-14ae-40ad-9176-0f6b40235d0b", "name": "<PERSON><PERSON>"}, {"parameters": {"content": "## FreeImage.host API KEY\n\nStep-by-Step: Create an API Key on FreeImage.host\n\n1. Log in to FreeImage.host  \n   - Visit: https://freeimage.host\n\n2. Navigate to the API Settings  \n   - Click menu (top left corner)\n   - Click the “API” tab or use link: https://freeimage.host/page/api\n   - Use API key specified", "height": 300, "width": 740, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, 1440], "id": "9ef067af-86a9-4f5c-98e2-3d8bbc506367", "name": "Sticky Note1"}, {"parameters": {"content": "## Midjourney - Description and Text To Image\n\n## piapi.ai API KEY\n\nStep-by-Step: Create an API Key on piapi.ai\n\n1. Go to piapi.ai  \n   - Visit: https://www.piapi.ai  \n\n2. Generate an API Key  \n   - In workspace -> settings -> API Keys\n   - Click **“Generate API Key”**  \n   - Copy the key provided  \n   - No sign-in or account creation required  \n   - Save the key securely (it won't be shown again)\n\n---\n## Piapia Billing\n\n1. Log In to PiAPI  \n\n2. Navigate to Your Workspace  \n   - Go to: https://piapi.ai/workspace  \n   - This is where you manage all projects, API keys, and billing\n\n3. Open the Billing Section  \n   - Within the workspace, locate the **Billing** or **Credits** section  \n   - Here you can view your current credit balance, usage, and subscription status\n\n4. Add Payment Method or Buy Credits  \n   - Select a plan or pay-as-you-go option  \n   - Enter your credit or debit card details when prompted  \n   - Purchase credits or activate subscriptions for services like Midjourney, Suno, or Kling\n\n5. Monitor Usage and Quotas  \n   - Keep track of remaining credits and consumption in real-time  \n   - Set alerts or limits if supported to avoid unexpected charges\n\n---\n\n## Midjourney Description (Image to Text)\n\n1. Use the Midjourney Image Description Endpoint  \n   - Endpoint: https://api.piapi.ai/api/v1/task \n   - Use image URL\n   - Specify \"describe\" in POST body\n   - Output: short natural language prompt\n\n2. Docs: https://piapi.ai/docs/midjourney-api/describe\n\n---\n\n## Midjourney Text to Image\n\n1. Use the Midjourney Text-to-Image Endpoint  \n   - Endpoint: https://api.piapi.ai/api/v1/task \n   - Use prompt\n   - Specify \"imagine\" in POST body\n   - Output: short natural language prompt\n\n2. Docs: https://piapi.ai/docs/midjourney-api/imagine", "height": 1520, "width": 740, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [120, 1780], "id": "97196e3b-0a71-4ab3-9f94-2573a755009e", "name": "Sticky Note3"}, {"parameters": {"content": "## Hedra Image + Audio to Video\n\n## Hedra API KEY\n\nStep-by-Step: Create an API Key on Hedra\n\n1. Sign Up at Hedra  \n   - Visit: https://www.hedra.com  \n   - Click **“Sign Up”** and create an account\n\n2. Generate an API Key  \n   - Once logged in, go to: https://www.hedra.com/api-profile\n   - Copy and save your API key securely\n\n---\n\n## Hedra Billing\n\n1. Open Your Hedra Account  \n   - Visit: https://app.hedra.com  \n   - Click your profile icon  \n   - Select **Billing** from the dropdown\n\n2. Add Payment Method  \n   - Add a credit/debit card to enable generation features  \n   - Some features may be restricted without billing set up\n\n---\n\n## Hedra API Documentation\n\n- Full API Docs: https://api.hedra.com/web-app/redoc#tag/Public\n\n---\n\n## Hedra API Usage Overview\n\n### Create Asset  \n- Endpoint: `https://api.hedra.com/web-app/public/assets`  \n- Purpose: Registers a new asset entry and returns an ID for upload  \n- Method: `POST`  \n- Returns: Asset ID (`id`) used in the upload step\n\n### Upload Asset  \n- Endpoint: `https://api.hedra.com/web-app/public/assets/:assetId/upload`  \n- Purpose: Uploads binary image or audio data to a previously created asset  \n- Important:  \n  - He<PERSON> expects **binary file data**, **not a URL**\n\n### Generate Video  \n- Endpoint: `https://api.hedra.com/web-app/public/generations`  \n- Purpose: Starts a video generation process using uploaded assets  \n- Returns: A file stream with MIME type `orchestration/stream`  \n- Important:  \n  - You must convert the streamed response to a downloadable `.mp4` file for YT upload", "height": 1860, "width": 740, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [940, 1440], "id": "8a424f59-f75a-432d-9fe6-76041fab2df1", "name": "Sticky Note8"}, {"parameters": {"pollTimes": {"item": [{}]}, "triggerOn": "specificFolder", "folderToWatch": {"__rl": true, "value": "15Dok8RuuuxmqxSlVJU6PuVab7QigAzJP", "mode": "list", "cachedResultName": "animated-pod-videos", "cachedResultUrl": "https://drive.google.com/drive/folders/15Dok8RuuuxmqxSlVJU6PuVab7QigAzJP"}, "event": "fileCreated", "options": {}}, "type": "n8n-nodes-base.googleDriveTrigger", "typeVersion": 1, "position": [-1380, 3880], "id": "0d809b7e-15ea-4302-9eb3-3b870d7da8bf", "name": "Google Drive Upload BabyPod", "credentials": {"googleDriveOAuth2Api": {"id": "MSEsl3NjCXoJ46AE", "name": "Google Drive brsm"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-1160, 3880], "id": "002358df-818c-410f-af99-552a116906ea", "name": "Get Video2", "credentials": {"googleDriveOAuth2Api": {"id": "MSEsl3NjCXoJ46AE", "name": "Google Drive brsm"}}}, {"parameters": {"method": "POST", "url": "https://api.apyhub.com/extract/video/audio/file?output=test-sample", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "apy-token"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "video", "inputDataFieldName": "data"}, {"name": "duration", "value": "5"}, {"name": "output_format", "value": "mp3"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-940, 3880], "id": "2fd3c10a-2903-4d7c-9b02-ea30dab93acc", "name": "Video To Audio1"}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-720, 3880], "id": "08d221fb-a44c-48aa-9414-10cb3e14c418", "name": "Wait 2 mins #2", "webhookId": "273ec8bd-2529-4793-9e7f-f618376ed356"}, {"parameters": {"assignments": {"assignments": [{"id": "58b106e4-0292-45f2-a98f-13e6107dd039", "name": "originalFilename", "value": "={{ $json.originalFilename.split(\".\")[0] }}", "type": "string"}]}, "options": {}}, "type": "n8n-nodes-base.set", "typeVersion": 3.4, "position": [-1380, 4200], "id": "56ab264f-9f1f-459d-b33d-85b1f6b307ac", "name": "Get Title Base From Video"}, {"parameters": {"resource": "fileFolder", "queryString": "={{ $json.originalFilename }}.png", "limit": 1, "filter": {}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-1160, 4200], "id": "ddfeb665-092b-4840-800b-cddfc010992f", "name": "Get Image ID", "credentials": {"googleDriveOAuth2Api": {"id": "MSEsl3NjCXoJ46AE", "name": "Google Drive brsm"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [-940, 4200], "id": "886f6026-cc28-44cc-8ff8-597c4a671cbe", "name": "Download Image", "credentials": {"googleDriveOAuth2Api": {"id": "MSEsl3NjCXoJ46AE", "name": "Google Drive brsm"}}}, {"parameters": {"method": "POST", "url": "https://freeimage.host/api/1/upload", "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"name": "key"}, {"name": "action", "value": "upload"}, {"parameterType": "formBinaryData", "name": "source", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-720, 4200], "id": "fcc04a9f-4e52-421b-ae5a-980e9e61bc55", "name": "FreeImageHost - Upload Image"}, {"parameters": {"amount": 2, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-1380, 4400], "id": "e91a9984-649f-42f5-808f-eb1fc34a4b94", "name": "Wait 2 mins", "webhookId": "ddd8bfa8-a3ac-437d-8ada-6f3350a713d7"}, {"parameters": {"method": "POST", "url": "https://api.piapi.ai/api/v1/task", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"describe\",\n  \"input\": {\n    \"image_url\": \"{{ $json.image.url }}\",\n    \"process_mode\": \"fast\"\n  },\n  \"config\": {\n    \"service_mode\": \"public\",\n    \"webhook_config\": {\n      \"endpoint\": \"\",\n      \"secret\": \"\"\n    }\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1160, 4400], "id": "d8f87ec5-55b8-481a-ad49-8221081f2f8f", "name": "Midjourney - Image Describe"}, {"parameters": {"amount": 4, "unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-940, 4400], "id": "bbfd5427-b045-4b53-8d6a-a684c807a9e2", "name": "Wait 4 mins", "webhookId": "fcba5b6f-a55c-46b2-a8ae-f9fea5e54aa6"}, {"parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-720, 4400], "id": "77921190-a14f-40f6-9b9f-191e4a9c6da8", "name": "Midjourney - Get Description"}, {"parameters": {"modelId": {"__rl": true, "value": "gpt-4", "mode": "list", "cachedResultName": "GPT-4"}, "messages": {"values": [{"content": "You are an expert prompt engineer specialising in crafting hyperrealistic visual generation prompts from reference Midjourney description. Your job is to analyse the given image and describe here it with precise, vivid, and structured language optimised for AI image generation models (e.g., Midjourney). Prioritise clarity, object specificity, photorealism, physical proportions, and environmental context. Remove any branding, UI elements, or text from the description. Avoid generic phrases. Do not include actual names unless specifically instructed.\n\nAlso, do not include \"\\n\" or any other code within the prompt generated!", "role": "system"}, {"content": "=You are an advanced visual transformation AI specialised in generating photorealistic baby versions of described subjects while preserving all key visual, spatial, and contextual elements from the provided scene description.\n\nScene Description: {{ $json.data.output.description }}\n\nYour task is to transform the primary subject described in the scene into a photorealistic baby version of themselves, while maintaining their pose, expression, clothing, environment, and overall context exactly as described. This must not be a cartoon or stylized rendering — it is a 1:1 naturalistic reinterpretation based strictly on the textual description.\n\nTransformation Objectives:\n\n1. Facial Continuity & Realism  \n• Retain the subject’s core identity — including facial structure, skin tone, and distinguishing features — adjusted only to reflect natural baby proportions.  \n• Eyes should remain recognisably expressive and proportionally enlarged to match infant symmetry.  \n• Skin must appear smooth, soft, and realistic, with no artificial gloss or exaggeration.  \n\n2. Pose & Expression Fidelity  \n• Maintain the exact pose, body orientation, and facial expression as described.  \n• Adjust limbs to match the proportions of a seated 16-month-old baby while preserving gesture and interaction with the environment.  \n\n3. Clothing & Accessories Consistency  \n• Preserve all described clothing, accessories, and visible textures — resizing elements as needed for a baby’s body without distortion.  \n• Do not introduce baby-specific items or modify the wardrobe in any way.  \n\n4. Environmental Preservation  \n• Reproduce the environment in full detail as described — including background objects, signage, lighting, and layout.  \n• Keep camera angle, depth of field, and light direction intact and the exact same as described.  \n\n5. Photorealistic Rendering Goals  \n• Ensure all elements — lighting, shadows, reflections, and materials — appear physically plausible and naturally rendered.  \n• Avoid any synthetic effects, cartoon exaggeration, or artificial smoothness.  \n\n6. Strict Visual Constraints  \n• Do not add or remove any objects.  \n• Do not alter scene composition or perspective.  \n• No overlays, text, UI elements, watermarks, or framing effects.\n\nThe result should be a single high-fidelity prompt to generate an image, fully aligned with the description, and in raw text output only. Do not prepend \"Output:\" or include line breaks like \"/n\".\n\nDON'T INCLUDE LINE BREAKS ANYWHERE IN THE RESPONSE, no \\n.\n\nEnsure the full prompt remains under 700 characters."}]}, "options": {}}, "type": "@n8n/n8n-nodes-langchain.openAi", "typeVersion": 1.8, "position": [-1400, 4640], "id": "9c8e453d-0359-49aa-b063-be0b48be4813", "name": "Combine Description With Prompt", "credentials": {"openAiApi": {"id": "eYkU6oZEtL5ZYl8C", "name": "OpenAi Main"}}}, {"parameters": {"content": "## OpenAI API KEY\n\nStep-by-Step: Create an API Key on OpenAI\n\n1. Log in to OpenAI  \n   - Visit: https://platform.openai.com  \n   - Sign in with your OpenAI account (or create one if you don't have one)\n\n2. Navigate to the API Keys Page  \n   - Click your profile icon (top-right corner)  \n   - Select “API Keys” from the dropdown  \n   - Or directly visit: https://platform.openai.com/account/api-keys\n\n3. Create a New Secret Key  \n   - Click the “Create new secret key” button  \n   - Optionally name it for easy identification  \n   - Click “Create secret key”\n\n## Steps to Set Up Billing on OpenAI Platform\n\n1. Log in to OpenAI  \n   - Visit: https://platform.openai.com  \n   - Sign in with your account\n\n2. Go to the Billing Section  \n   - Click your profile icon (top right)  \n   - Select “Manage account”  \n   - Go to the “Billing” tab  \n   - Or directly visit: https://platform.openai.com/account/billing\n\n3. Add a Payment Method  \n   - Click on “Payment methods”  \n   - Add your credit or debit card  \n   - Confirm through any verification prompts", "height": 1860, "width": 740}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1480, 1440], "id": "60c8d083-c1bd-4d3f-ac34-74bf08f81f06", "name": "Sticky Note10"}, {"parameters": {"jsCode": "const raw = $input.first().json.message.content\nconst cleaned = raw\n  .replace(/[0-9]️⃣/g, '')             // Remove emoji numbers like 1️⃣\n  .replace(/--ar\\s*\\d+:\\d+/g, '')     // Remove --ar resolution tags\n  .replace(/\\\\n|[\\r\\n]+/g, ' ')       // Replace newline characters with space\n  .replace(/\"/g, '')                  // Remove double quotes\n  .replace(/^\\d+[.)]\\s*/gm, '')       // Remove numbered list prefixes like \"1.\" or \"2)\"\n  .replace(/\\s+/g, ' ')               // Collapse multiple spaces\n  .trim();                            // Trim surrounding spaces\n\nreturn [{ json: { cleanedDescription: cleaned } }];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [-940, 4640], "id": "e35803c9-da05-4f0b-9056-c3e87dca2f2a", "name": "Code"}, {"parameters": {"method": "POST", "url": "https://api.piapi.ai/api/v1/task", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"model\": \"midjourney\",\n  \"task_type\": \"imagine\",\n  \"input\": {\n    \"prompt\": \"{{ $json.cleanedDescription }}\",\n    \"aspect_ratio\": \"9:16\",\n    \"process_mode\": \"fast\",\n    \"skip_prompt_check\": true\n  }\n} ", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-720, 4640], "id": "3eae0eb2-a988-49f5-9ce4-945cfc376aa6", "name": "Midjourney - Text To Image"}, {"parameters": {"unit": "minutes"}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-1380, 4880], "id": "95293a22-d5c0-4a30-a50a-302002e38ef5", "name": "Wait 5 mins", "webhookId": "c9ad815d-4764-4342-b43b-af2cda475dcd"}, {"parameters": {"url": "=https://api.piapi.ai/api/v1/task/{{ $json.data.task_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "Accept", "value": "application/json"}, {"name": "x-api-key"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-1160, 4880], "id": "079bc7d2-a996-4691-a357-10fd25704560", "name": "Midjourney - Get Image"}, {"parameters": {"amount": 15}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [-940, 4880], "id": "57063611-9816-43ee-a711-5062fc0055f0", "name": "Wait 15 secs", "webhookId": "44d1d568-a56a-4916-9abd-db1c69f8a81f"}, {"parameters": {"url": "={{ $json.data.output.temporary_image_urls[1] }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-720, 4880], "id": "573808c2-ec4e-48dc-b70d-2ede48fb132d", "name": "Download Midjourney Image"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/assets", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"name\": \"baby-podcaster-image\",\n  \"type\": \"image\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-440, 4880], "id": "944ca11a-f7c4-45eb-9f95-1d29cc6cdaa0", "name": "Hedra - Create Image Asset"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-180, 4700], "id": "47accec9-26fa-4d01-81b7-95aa5eea9d61", "name": "Merge Asset And Binary Image"}, {"parameters": {"jsCode": "const binaryItem = items.find(i => i.binary && Object.keys(i.binary).length > 0);\nconst metadataItem = items.find(i => i.json && i.json.id && i.json.type);\n\nreturn [\n  {\n    json: {\n      id: metadataItem.json.id,\n      type: metadataItem.json.type,\n      name: metadataItem.json.name || 'uploaded-asset'\n    },\n    binary: {\n        data: binaryItem.binary.data\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [0, 4700], "id": "f3007a7d-3f70-4504-bbb1-d01b7663f2a4", "name": "Split Binary And Metadata #2"}, {"parameters": {"method": "POST", "url": "=https://api.hedra.com/web-app/public/assets/{{ $json.id }}/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key"}, {"name": "Content-Type", "value": "multipart/form-data"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 4700], "id": "507e4ee6-dd84-4e20-b601-f5a8a6435106", "name": "Hedra - Upload Image"}, {"parameters": {"jsCode": "const binaryItem = items.find(i => i.binary && i.binary.data);\nconst metadataItem = items.find(i => i.json && i.json.id && i.json.type);\n\nreturn [\n  {\n    json: {\n      id: metadataItem.json.id,\n      type: metadataItem.json.type,\n      name: metadataItem.json.name || 'uploaded-asset'\n    },\n    binary: binaryItem.binary\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [20, 4040], "id": "8144327f-7282-4e52-9b8f-0b3a4f6621a3", "name": "Split Binary And Metadata #"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/assets", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "{\n  \"name\": \"baby-podcaster-audio\",\n  \"type\": \"audio\"\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [-460, 3880], "id": "1939a149-713a-426a-afdd-13fc5c54e7e3", "name": "Hedra - Create Audio Asset"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [-180, 4040], "id": "2620d8f7-e7f6-4cef-99c7-be91961724f7", "name": "Merge Asset And Binary Audio"}, {"parameters": {"method": "POST", "url": "=https://api.hedra.com/web-app/public/assets/{{ $json.id }}/upload", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key"}, {"name": "Content-Type", "value": "multipart/form-data"}]}, "sendBody": true, "contentType": "multipart-form-data", "bodyParameters": {"parameters": [{"parameterType": "formBinaryData", "name": "file", "inputDataFieldName": "data"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [220, 4040], "id": "13eef451-68e9-4f6f-828d-f43b00891ef7", "name": "Hedra - Upload Audio"}, {"parameters": {}, "type": "n8n-nodes-base.merge", "typeVersion": 3.1, "position": [660, 4240], "id": "f9c4552d-51f7-4a9c-abec-316988703b3c", "name": "<PERSON><PERSON>"}, {"parameters": {"jsCode": "let image_asset_id = null;\nlet audio_asset_id = null;\n\nfor (const item of items) {\n  const { type, id } = item.json;\n  if (type === 'image') {\n    image_asset_id = id;\n  } else if (type === 'audio') {\n    audio_asset_id = id;\n  }\n}\n\nreturn [\n  {\n    json: {\n      image_asset_id,\n      audio_asset_id\n    }\n  }\n];"}, "type": "n8n-nodes-base.code", "typeVersion": 2, "position": [880, 4240], "id": "75705725-cf76-4b43-94fb-e1396948a011", "name": "Split Image And Audio Asset Ids"}, {"parameters": {"method": "POST", "url": "https://api.hedra.com/web-app/public/generations", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key"}, {"name": "Content-Type", "value": "application/json"}]}, "sendBody": true, "specifyBody": "json", "jsonBody": "={\n  \"type\": \"video\",\n  \"ai_model_id\": \"d1dd37a3-e39a-4854-a298-6510289f9cf2\",\n  \"start_keyframe_id\": \"{{ $json.image_asset_id }}\",\n  \"audio_id\": \"{{ $json.audio_asset_id }}\",\n  \"generated_video_inputs\": {\n    \"text_prompt\": \"A baby podcast host seated in front of a microphone, speaking with calm intensity and natural focus. Subtle facial expressions, minimal head movement, steady eye contact with the camera. Studio lighting with a professional podcast setup in the background.\",\n    \"resolution\": \"720p\",\n    \"aspect_ratio\": \"9:16\",\n    \"duration_ms\": 5000\n  }\n}", "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1100, 4240], "id": "5b9d1b82-2598-472d-bba5-1c2030b9e2aa", "name": "<PERSON><PERSON> - Render Video"}, {"parameters": {"amount": 8}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1320, 4240], "id": "1f88bbff-ee73-445b-8901-e974a2d99f23", "name": "Wait 8 mins", "webhookId": "89c1e462-6331-4a5e-9af7-a1a336352d5c"}, {"parameters": {"url": "=https://api.hedra.com/web-app/public/assets?type=video&ids={{ $json.asset_id }}", "sendHeaders": true, "headerParameters": {"parameters": [{"name": "X-Api-Key"}]}, "options": {}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1540, 4240], "id": "19d7064c-51df-4bf6-85a5-83389197564e", "name": "<PERSON><PERSON> - Get BabyPod Video"}, {"parameters": {"amount": 15}, "type": "n8n-nodes-base.wait", "typeVersion": 1.1, "position": [1760, 4240], "id": "dcce78e6-32ef-4aba-a3d1-04014ec1a3a7", "name": "Wait 15 secs #2", "webhookId": "ce45396b-7b64-4422-ad8e-1b3e6408a297"}, {"parameters": {"url": "={{ $json.asset.url }}", "options": {"response": {"response": {"responseFormat": "file"}}}}, "type": "n8n-nodes-base.httpRequest", "typeVersion": 4.2, "position": [1980, 4240], "id": "297051be-0bfc-4a77-a29d-15db3d506e72", "name": "Download <PERSON><PERSON>od Video"}, {"parameters": {"name": "=pod-{{ $json.id }}", "driveId": {"__rl": true, "mode": "list", "value": "My Drive"}, "folderId": {"__rl": true, "value": "1GvxiNnEwfYnTFjLWduS8hkgafB_ZTajJ", "mode": "list", "cachedResultName": "animated-pod-hedra-video", "cachedResultUrl": "https://drive.google.com/drive/folders/1GvxiNnEwfYnTFjLWduS8hkgafB_ZTajJ"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [660, 4600], "id": "4e2f87f3-a4f5-4c45-9058-66d1273b6578", "name": "Upload BabyPod Vid To Drive", "credentials": {"googleDriveOAuth2Api": {"id": "MSEsl3NjCXoJ46AE", "name": "Google Drive brsm"}}}, {"parameters": {"operation": "download", "fileId": {"__rl": true, "value": "={{ $json.id }}", "mode": "id"}, "options": {}}, "type": "n8n-nodes-base.googleDrive", "typeVersion": 3, "position": [880, 4600], "id": "9b707076-bac5-4c6c-9d1f-e6d37e9c7066", "name": "Download - Hedra mp4", "credentials": {"googleDriveOAuth2Api": {"id": "MSEsl3NjCXoJ46AE", "name": "Google Drive brsm"}}}, {"parameters": {"resource": "video", "operation": "upload", "title": "=pod-{{ $json.id }}", "regionCode": "GB", "categoryId": "24", "options": {"description": "Funny Baby Podcast", "privacyStatus": "unlisted"}}, "type": "n8n-nodes-base.youTube", "typeVersion": 1, "position": [1100, 4600], "id": "b6ce497c-9a9b-4629-a552-2648c3d441c6", "name": "YouTube", "credentials": {"youTubeOAuth2Api": {"id": "DvcPwkOP7IOLRaqz", "name": "YouTube account"}}}, {"parameters": {"conditions": {"options": {"caseSensitive": true, "leftValue": "", "typeValidation": "strict", "version": 2}, "conditions": [{"id": "d3a8fcba-88cb-4961-8e5e-61379ce87898", "leftValue": "={{ $json.uploadId }}", "rightValue": "undefined", "operator": {"type": "string", "operation": "notEquals"}}], "combinator": "and"}, "options": {}}, "type": "n8n-nodes-base.if", "typeVersion": 2.2, "position": [1320, 4600], "id": "3400b43a-7fb9-4fc2-a2e0-6a820af2ae5a", "name": "If Successfully Published"}, {"parameters": {"sendTo": "<EMAIL>", "subject": "Successfully Posted BabyPod Via n8n", "emailType": "text", "message": "=Auto generated message.\n\nSuccessfully posted to YT!!\n\nTitle: {{ $('Download - Hedra mp4').item.json.name }}\n\nBest,\nBot", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1540, 4500], "id": "4465abff-c597-4b6b-a77b-0a0bbad1d6e4", "name": "Gmail Success", "webhookId": "b0084c92-5535-44de-85c7-c2c56458b0b6", "credentials": {"gmailOAuth2": {"id": "4e7tUrzwWbQmrCu6", "name": "Gmail account brsm"}}}, {"parameters": {"sendTo": "<EMAIL>", "subject": "Failed to Post BabyPod Via n8n", "emailType": "text", "message": "=Auto generated message.\n\nFailed to post to YT!!\n\nTitle: {{ $('Download - Hedra mp4').item.json.name }}\n\nBest,\nBot", "options": {}}, "type": "n8n-nodes-base.gmail", "typeVersion": 2.1, "position": [1540, 4720], "id": "da51025b-787c-40c9-b218-e4f7e43350c1", "name": "<PERSON><PERSON>", "webhookId": "********-860e-4e21-853f-407e5d8a4209", "credentials": {"gmailOAuth2": {"id": "NWVTDPHQPoBdtxvQ", "name": "Gmail account Other"}}}, {"parameters": {"content": "# Convert Video to Audio", "height": 280, "width": 900, "color": 3}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1460, 3800], "id": "5283411e-ba3c-4a92-a0c6-ef2b2b256c1e", "name": "Sticky Note2"}, {"parameters": {"content": "# Create Image\n", "height": 1040, "width": 900, "color": 4}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-1460, 4100], "id": "a82b7b51-a19b-4d69-aec5-7f6b6087424f", "name": "Sticky Note7"}, {"parameters": {"content": "# Create and Upload Assets To Hedra\n", "height": 1340, "width": 1000, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [-520, 3800], "id": "fff75374-1132-4d26-939b-eabbd927006b", "name": "Sticky Note4"}, {"parameters": {"content": "# Generate Video\n", "height": 300, "width": 1620, "color": 5}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [600, 4140], "id": "76a56087-bc13-4432-95ee-e20c4367705b", "name": "Sticky Note5"}, {"parameters": {"content": "# Upload Video\n", "height": 420, "width": 1620}, "type": "n8n-nodes-base.stickyNote", "typeVersion": 1, "position": [600, 4460], "id": "5fd970f7-b39f-47ac-aef3-c255f112f92d", "name": "Sticky Note6"}], "pinData": {}, "connections": {"Google Drive Upload BabyPod": {"main": [[{"node": "Get Video2", "type": "main", "index": 0}, {"node": "Get Title Base From Video", "type": "main", "index": 0}]]}, "Get Video2": {"main": [[{"node": "Video To Audio1", "type": "main", "index": 0}]]}, "Video To Audio1": {"main": [[{"node": "Wait 2 mins #2", "type": "main", "index": 0}]]}, "Get Title Base From Video": {"main": [[{"node": "Get Image ID", "type": "main", "index": 0}]]}, "Get Image ID": {"main": [[{"node": "Download Image", "type": "main", "index": 0}]]}, "Download Image": {"main": [[{"node": "FreeImageHost - Upload Image", "type": "main", "index": 0}]]}, "FreeImageHost - Upload Image": {"main": [[{"node": "Wait 2 mins", "type": "main", "index": 0}]]}, "Wait 2 mins": {"main": [[{"node": "Midjourney - Image Describe", "type": "main", "index": 0}]]}, "Midjourney - Image Describe": {"main": [[{"node": "Wait 4 mins", "type": "main", "index": 0}]]}, "Wait 4 mins": {"main": [[{"node": "Midjourney - Get Description", "type": "main", "index": 0}]]}, "Midjourney - Get Description": {"main": [[{"node": "Combine Description With Prompt", "type": "main", "index": 0}]]}, "Combine Description With Prompt": {"main": [[{"node": "Code", "type": "main", "index": 0}]]}, "Code": {"main": [[{"node": "Midjourney - Text To Image", "type": "main", "index": 0}]]}, "Midjourney - Text To Image": {"main": [[{"node": "Wait 5 mins", "type": "main", "index": 0}]]}, "Wait 5 mins": {"main": [[{"node": "Midjourney - Get Image", "type": "main", "index": 0}]]}, "Midjourney - Get Image": {"main": [[{"node": "Wait 15 secs", "type": "main", "index": 0}]]}, "Wait 15 secs": {"main": [[{"node": "Download Midjourney Image", "type": "main", "index": 0}]]}, "Download Midjourney Image": {"main": [[{"node": "Hedra - Create Image Asset", "type": "main", "index": 0}, {"node": "Merge Asset And Binary Image", "type": "main", "index": 1}]]}, "Hedra - Create Image Asset": {"main": [[{"node": "Merge Asset And Binary Image", "type": "main", "index": 0}]]}, "Merge Asset And Binary Image": {"main": [[{"node": "Split Binary And Metadata #2", "type": "main", "index": 0}]]}, "Split Binary And Metadata #2": {"main": [[{"node": "Hedra - Upload Image", "type": "main", "index": 0}]]}, "Split Binary And Metadata #": {"main": [[{"node": "Hedra - Upload Audio", "type": "main", "index": 0}]]}, "Wait 2 mins #2": {"main": [[{"node": "Hedra - Create Audio Asset", "type": "main", "index": 0}, {"node": "Merge Asset And Binary Audio", "type": "main", "index": 1}]]}, "Hedra - Create Audio Asset": {"main": [[{"node": "Merge Asset And Binary Audio", "type": "main", "index": 0}]]}, "Merge Asset And Binary Audio": {"main": [[{"node": "Split Binary And Metadata #", "type": "main", "index": 0}]]}, "Hedra - Upload Audio": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}, "Hedra - Upload Image": {"main": [[{"node": "<PERSON><PERSON>", "type": "main", "index": 1}]]}, "Merge Assets": {"main": [[{"node": "Split Image And Audio Asset Ids", "type": "main", "index": 0}]]}, "Split Image And Audio Asset Ids": {"main": [[{"node": "<PERSON><PERSON> - Render Video", "type": "main", "index": 0}]]}, "Hedra - Render Video": {"main": [[{"node": "Wait 8 mins", "type": "main", "index": 0}]]}, "Wait 8 mins": {"main": [[{"node": "<PERSON><PERSON> - Get BabyPod Video", "type": "main", "index": 0}]]}, "Hedra - Get BabyPod Video": {"main": [[{"node": "Wait 15 secs #2", "type": "main", "index": 0}]]}, "Wait 15 secs #2": {"main": [[{"node": "Download <PERSON><PERSON>od Video", "type": "main", "index": 0}]]}, "Download Hedra BabyPod Video": {"main": [[{"node": "Upload BabyPod Vid To Drive", "type": "main", "index": 0}]]}, "Upload BabyPod Vid To Drive": {"main": [[{"node": "Download - Hedra mp4", "type": "main", "index": 0}]]}, "Download - Hedra mp4": {"main": [[{"node": "YouTube", "type": "main", "index": 0}]]}, "YouTube": {"main": [[{"node": "If Successfully Published", "type": "main", "index": 0}]]}, "If Successfully Published": {"main": [[{"node": "Gmail Success", "type": "main", "index": 0}], [{"node": "<PERSON><PERSON>", "type": "main", "index": 0}]]}}, "active": false, "settings": {"executionOrder": "v1"}, "versionId": "e381da55-405b-49e8-97fa-5bf42f991e21", "meta": {"templateCredsSetupCompleted": true, "instanceId": "70e33186deff6b1041cabd7d16d126d81f110932e6df14bfefaa8138e86d6207"}, "id": "Uksq8fwj8gYyWoSZ", "tags": [{"createdAt": "2025-04-12T13:48:49.966Z", "updatedAt": "2025-04-12T13:48:49.966Z", "id": "G0CYS9bWFUALIdRT", "name": "content-creation-test"}]}